import type {
  BasicDepartment,
  BasicItem,
  DetailedItem,
  StoreInfo,
  SvelteFetch,
  Totals,
} from "$lib/types";
import { getContext, setContext } from "svelte";
import { CheckoutError } from "$lib/services/errors/checkout/CheckoutError.ts";
import { getTotals } from "$lib/api/get-totals/get-totals";
export interface IBridgeClient {
  getMenu(): Promise<{
    departments: BasicDepartment[];
    items: BasicItem[];
    storeInfo: StoreInfo;
  }>;

  getDetailedItem(itemId: string): Promise<DetailedItem>;

  getHealth(): Promise<{ ok: boolean }>;

  getTotals(requestData: {
    items: DetailedItem[];
    promisedTime: number;
    subTotal: number;
  }): Promise<Totals>;
}

/**
 * Client for making authenticated requests to the bridge API, which exists as a middleman for communicating with
 * POS systems.
 */
export class BridgeClient implements IBridgeClient {
  fetch: SvelteFetch;
  deviceId: string;

  constructor(fetch: SvelteFetch, deviceId: string) {
    this.fetch = fetch;
    this.deviceId = deviceId;
  }

  async getMenu() {
    const response = await this.fetch(
      `/api/bridge/${this.deviceId}/item/online/menu`,
      {
        method: "GET",
      },
    );

    const data = (await response.json()) as {
      departments: BasicDepartment[];
      items: { [departmentId: string]: BasicItem[] };
      storeInfo: StoreInfo;
    };

    const itemsFlattened = Object.values(data.items ?? []).flatMap((items) => {
      /**
       * Handle any falsy response we may get back from the api by ignoring.
       */
      if (!items) {
        return [];
      }
      return items;
    });

    return {
      departments: data.departments,
      items: itemsFlattened,
      storeInfo: data.storeInfo,
    };
  }

  async getDetailedItem(itemId: string) {
    const response = await this.fetch(
      `/api/bridge/${this.deviceId}/item/online/${itemId}`,
      {
        method: "GET",
      },
    );

    const data = (await response.json()) as DetailedItem;

    return data;
  }

  async getTotals(requestData: {
    items: DetailedItem[];
    promisedTime: number;
    subTotal: number;
  }): Promise<Totals> {
    return getTotals(
      {
        ...requestData,
        deviceId: this.deviceId,
      },
      this.fetch,
    );
  }

  async getHealth() {
    try {
      const response = await this.fetch(
        `/api/bridge/${this.deviceId}/sale/online/health`,
        {
          method: "GET",
        },
      );

      const { ok } = await response.json();

      if (!ok) {
        throw new CheckoutError({
          name: "RESTAURANT_OFFLINE",
          message: "Restaurant is offline and cannot process orders",
          cause: response,
        });
      }

      return { ok };
    } catch (error) {
      throw new CheckoutError({
        name: "HEALTH_CHECK_FAILED",
        message: "Health check failed",
        cause: error,
      });
    }
  }
}

export const getBridgeClient = (): IBridgeClient => {
  return getContext<IBridgeClient>("bridgeClient");
};

export const setBridgeClient = (bridgeClient: IBridgeClient) => {
  return setContext("bridgeClient", bridgeClient);
};
