import { CheckoutError } from "$lib/services/errors/checkout/CheckoutError";
import type { DetailedItem, Totals, SvelteFetch } from "$lib/types";

export const getTotals = async (
  requestData: {
    items: DetailedItem[];
    promisedTime: number;
    subTotal: number;
    deviceId: string;
  },
  fetch: SvelteFetch,
): Promise<Totals> => {
  const response = await fetch(
    `/api/bridge/${requestData.deviceId}/sale/online/totals`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    },
  );

  const totals = await response.json();

  if (!response.ok) {
    throw new CheckoutError({
      name: totals.name || "GET_TOTALS",
      message: totals.message || "Failed to get totals",
      cause: totals.cause,
    });
  }

  return totals;
};
